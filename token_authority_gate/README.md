# Token Authority Gate KRNL Kernel

## Overview

The Token Authority Gate kernel validates ERC-20 token balance thresholds and returns authorization status. It checks if a specified Ethereum address holds a minimum required balance of a given ERC-20 token, making it useful for token-gated access control, DeFi protocols, and membership verification systems.

## Installation

1. Clone or download this kernel implementation:
```bash
git clone <repository-url>
cd token_authority_gate
```

2. Install dependencies:
```bash
npm install
```

The kernel uses exact dependency versions:
- `ethers@6.13.4` - Ethereum library for blockchain interactions
- `dotenv@16.4.5` - Environment variable management

## Configuration

1. Copy the environment template:
```bash
cp .env.example .env
```

2. Configure your Ethereum RPC endpoint in `.env`:
```bash
RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
```

**Supported RPC Providers:**
- Infura: `https://mainnet.infura.io/v3/YOUR_PROJECT_ID`
- Alchemy: `https://eth-mainnet.alchemyapi.io/v2/YOUR_API_KEY`
- QuickNode: `https://YOUR_ENDPOINT.quiknode.pro/YOUR_API_KEY/`
- Public RPC: `https://ethereum.publicnode.com` (rate limited)

## Testing

Run the comprehensive smoke test to verify functionality:

```bash
npm run test:smoke
```

The smoke test validates:
- ✅ Real blockchain data retrieval using Vitalik's address
- ✅ USDC token balance checking with different thresholds
- ✅ Proper JSON response structure validation
- ✅ Error handling for invalid inputs
- ✅ Network connectivity and RPC functionality

**Expected Output:**
```
🧪 Running Token Authority Gate Smoke Test...

📋 Test 1: USDC Balance Check - High Threshold
   ✅ Success (1250ms)
   📊 Result:
      Authorized: true/false
      Actual Balance: **********
      Human Readable: 1234 USDC
```

## Deployment

### IPFS Publishing

1. Ensure IPFS is installed and running:
```bash
ipfs --version
ipfs daemon
```

2. Publish to IPFS:
```bash
./publish.sh
```

**Expected Output:**
```
Publishing to IPFS...
Published with CID: bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi
IPFS URI: ipfs://bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi
```

## Registration

Register the kernel with KRNL using the CLI:

```bash
krnl-cli register \
  --id token_authority_gate \
  --version 0.1.0 \
  --ipfs-uri ipfs://YOUR_CID_HERE \
  --spec-file kernel-spec.yaml \
  --description "Validates ERC-20 token balance thresholds for authorization"
```

## Usage

### SDK Integration Example

```javascript
const { KRNLClient } = require('@krnl/sdk');

const client = new KRNLClient({
  apiKey: 'your-api-key'
});

async function checkTokenGate() {
  try {
    const result = await client.execute('token_authority_gate', {
      userAddress: '******************************************',
      tokenAddress: '******************************************', // USDC
      minBalance: '1000000' // 1 USDC (6 decimals)
    });

    console.log('Authorization:', result.authorized);
    console.log('Balance:', result.actualBalance);
    
    if (result.authorized) {
      console.log('✅ User has sufficient token balance');
    } else {
      console.log('❌ User does not meet minimum balance requirement');
    }
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkTokenGate();
```

### Input Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `userAddress` | string | Ethereum address to check | `******************************************` |
| `tokenAddress` | string | ERC-20 token contract address | `******************************************` |
| `minBalance` | string | Minimum balance in smallest unit | `1000000` (1 USDC) |

### Output Format

```json
{
  "authorized": true,
  "actualBalance": "**********"
}
```

### Common Token Addresses (Ethereum Mainnet)

| Token | Address | Decimals | Example Balance |
|-------|---------|----------|-----------------|
| USDC | `******************************************` | 6 | `1000000` = 1 USDC |
| USDT | `******************************************` | 6 | `1000000` = 1 USDT |
| DAI | `******************************************` | 18 | `1000000000000000000` = 1 DAI |
| WETH | `******************************************` | 18 | `1000000000000000000` = 1 WETH |

## Error Handling

The kernel provides comprehensive error handling:

- **Invalid Address**: `Invalid userAddress: must be a valid Ethereum address`
- **Missing Inputs**: `Missing required inputs: userAddress, tokenAddress, and minBalance are required`
- **Network Issues**: `Network error: Unable to connect to Ethereum RPC`
- **Contract Errors**: `Contract call failed: Invalid token contract or network issue`

## Development

### File Structure
```
token_authority_gate/
├── kernel-spec.yaml     # Kernel specification
├── index.js            # Main execution logic
├── test.js             # Comprehensive smoke tests
├── package.json        # Dependencies and scripts
├── .env.example        # Environment template
├── .gitignore          # Git ignore rules
├── publish.sh          # IPFS deployment script
└── README.md           # This documentation
```

### Local Development

1. Set up environment:
```bash
cp .env.example .env
# Edit .env with your RPC URL
```

2. Run tests:
```bash
npm run test:smoke
```

3. Test with custom parameters:
```bash
node -e "
const { execute } = require('./index.js');
execute({
  userAddress: 'YOUR_ADDRESS',
  tokenAddress: 'TOKEN_CONTRACT',
  minBalance: 'MIN_BALANCE'
}).then(console.log).catch(console.error);
"
```

## License

ISC License - See package.json for details.
